package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.dto.core.MCInstructor
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.springframework.stereotype.Component

@Component
@CompileStatic
class EthosColleagueInstructorReadTransforms {









    // Transform for individual instructor endpoint (returns MCInstructor)
    ITransformer<String, MCInstructor> mcInstructorTransformer = { TransformContext ctx, String body ->
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def instructor = new MCInstructor()

        // Use PERSON ID as primary identifier
        instructor.id = rootNode.id as String

        // Map names from PERSONS data
        mapNamesForMCInstructor(instructor, rootNode)

        // Map emails from PERSONS data
        mapEmailsForMCInstructor(instructor, rootNode)

        // Map instructor types and departments from INSTRUCTORS association data
        mapInstructorAssociationsFor<PERSON><PERSON>nstructor(instructor, ctx)

        // Map status from roles.role (ethos)
        mapStatusForMCInstructor(instructor, rootNode)

        return instructor
    }



    private void mapNamesForMCInstructor(MCInstructor instructor, Map<String, ?> personData) {
        def names = personData.names as List<Map<String, ?>>
        if (names) {
            instructor.names = names.collect { nameData ->
                def name = new MCInstructor.MCInstructorName()
                // Generate a unique ID for the name based on person ID and name type
                def nameTypeId = (nameData.type as Map<String, ?>)?.id ?: 'default'
                name.id = "${instructor.id}_name_${nameTypeId}"

                // Map preferred from names.preference (ethos)
                name.preferred = nameData.preference as Boolean

                name.title = nameData.title as String
                name.firstName = nameData.firstName as String
                name.middleName = nameData.middleName as String
                name.lastName = nameData.lastName as String
                name.suffix = nameData.lastName as String // Map from names.lastName (ethos)

                // Map name type
                if (nameData.type) {
                    def typeData = nameData.type as Map<String, ?>
                    name.type = new MCInstructor.MCInstructorNameType()
                    // Map from names.type.detail.id (ethos)
                    def nameDetailData = typeData.detail as Map<String, ?>
                    name.type.id = nameDetailData?.id as String
                    // Map from names.type.category (ethos)
                    name.type.name = typeData.category as String
                }

                return name
            }
        }
    }

    private void mapEmailsForMCInstructor(MCInstructor instructor, Map<String, ?> personData) {
        def emailAddresses = personData.emailAddresses as List<Map<String, ?>>
        if (emailAddresses) {
            instructor.emails = emailAddresses.collect { emailData ->
                def email = new MCInstructor.MCInstructorEmail()
                // Generate a unique ID for the email based on person ID and email type
                def emailTypeId2 = (emailData.type as Map<String, ?>)?.id ?: 'default'
                email.id = "${instructor.id}_email_${emailTypeId2}"

                // Map preferred from emails.preference (ethos)
                email.preferred = emailData.preference as Boolean
                email.address = emailData.address as String

                // Map email type
                if (emailData.type) {
                    def typeData = emailData.type as Map<String, ?>
                    email.type = new MCInstructor.MCInstructorEmailType()
                    // Map from emails.type.detail.id (ethos)
                    def emailDetailData2 = typeData.detail as Map<String, ?>
                    email.type.id = emailDetailData2?.id as String
                    // Map from emails.type.emailType (ethos)
                    email.type.name = typeData.emailType as String
                }

                return email
            }
        }
    }

    private void mapInstructorAssociationsForMCInstructor(MCInstructor instructor, TransformContext ctx) {
        def instructorAssocData = ctx.contextProps.get("ETHOS_ASSOC_INSTRUCTOR") as List<Map<String, ?>>

        println "DEBUG: instructorAssocData = ${instructorAssocData}"

        if (instructorAssocData) {
            // Extract unique instructor types and departments from instructors.category.id (ethos)
            def instructorTypes = [] as Set<Map<String, String>>
            def departments = [] as Set<Map<String, String>>

            instructorAssocData.each { instructorData ->
                // Extract instructor types from instructors.category.id (ethos)
                def category = instructorData.category as Map<String, ?>
                if (category?.id) {
                    instructorTypes.add([
                        id: category.id as String,
                        name: "instructor" // Default type name
                    ])

                    // Both instructorTypes and departments map from the same field: instructors.category.id (ethos)
                    departments.add([
                        id: category.id as String,
                        name: category.title as String ?: category.id as String,
                        code: category.code as String ?: category.id as String
                    ])
                }
            }

            // Map unique instructor types
            instructor.instructorTypes = instructorTypes.collect { typeData ->
                def type = new MCInstructor.MCInstructorType()
                type.id = typeData.id
                return type
            }

            // Map unique departments
            instructor.departments = departments.collect { deptData ->
                def dept = new MCInstructor.MCInstructorDepartment()
                dept.id = deptData.id
                return dept
            }
        } else {
            // If no instructor association data, set default values
            instructor.instructorTypes = []
            instructor.departments = []
        }
    }

    private void mapStatusForMCInstructor(MCInstructor instructor, Map<String, ?> personData) {
        // Map status from roles.role (ethos)
        def roles = personData.roles as List<Map<String, ?>>
        if (roles && !roles.isEmpty()) {
            // Use the first role's role field as status
            def firstRole = roles[0] as Map<String, ?>
            instructor.status = firstRole.role as String
        } else {
            // Default status if no roles found
            instructor.status = "A"
        }
    }

    ITransformer<String, MCInstructor> getMcInstructorTransformer() {
        return mcInstructorTransformer
    }
}
