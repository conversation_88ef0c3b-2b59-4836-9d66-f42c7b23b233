package org.moderncampus.integration.colleague.workflow.transform.instructor

import groovy.json.JsonOutput
import org.apache.camel.Exchange
import org.apache.camel.impl.DefaultCamelContext
import org.apache.camel.support.DefaultExchange
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.moderncampus.integration.transform.TransformContext

import static org.junit.jupiter.api.Assertions.*

class EthosColleagueInstructorReadTransformsTest {

    private EthosColleagueInstructorReadTransforms transforms
    private Exchange exchange
    @BeforeEach
    void setUp() {
        transforms = new EthosColleagueInstructorReadTransforms()
        exchange = new DefaultExchange(new DefaultCamelContext())
    }

    @Test
    void testTransformInstructorWithCompleteData() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "<PERSON>. <PERSON>",
                firstName: "<PERSON>",
                middleName: "<PERSON>",
                lastName: "<PERSON>",
                title: "Dr.",
                pedigree: "PhD"
            ]],
            dateOfBirth: "1975-05-15",
            gender: "male",
            credentials: [
                [type: "colleaguePersonId", value: "0001234"],
                [type: "ssn", value: "***********"]
            ],
            roles: [[role: "instructor", startOn: "2020-08-15"]]
        ]

        def instructorData = [
            id: "inst-8c005e2e-14fd-47f3-a772-eb88705818ef",
            instructor: [id: "8c005e2e-14fd-47f3-a772-eb88705818ef"],
            institutionalUnits: [
                [
                    department: [id: "dept-001", title: "Computer Science"],
                    percentage: 75.0
                ],
                [
                    department: [id: "dept-002", title: "Mathematics"],
                    percentage: 25.0
                ]
            ],
            tenure: [
                type: [id: "tenure-track"],
                startOn: "2020-08-15"
            ],
            preferences: [
                [type: "preferred", value: "true"],
                [type: "primary", value: "false"]
            ]
        ]

        exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", [instructorData])

        // When
        def ctx = new TransformContext()
        ctx.contextProps.put("ETHOS_ASSOC_INSTRUCTOR", [instructorData])
        def result = transforms.getMcInstructorTransformer().transform(ctx, JsonOutput.toJson(personData))

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)

        // Verify names structure
        assertEquals(1, result.names.size())
        assertEquals("John", result.names[0].firstName)
        assertEquals("Michael", result.names[0].middleName)
        assertEquals("Smith", result.names[0].lastName)
        assertEquals("Dr.", result.names[0].title)
        assertEquals("Smith", result.names[0].suffix) // Maps from lastName

        assertEquals("instructor", result.status)

        // Verify instructor-specific data
        assertEquals(2, result.departments.size())
        assertEquals("dept-001", result.departments[0].id)
        assertEquals("dept-002", result.departments[1].id)
    }

    @Test
    void testTransformInstructorWithoutInstructorData() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "John Smith",
                firstName: "John",
                lastName: "Smith"
            ]],
            credentials: [
                [type: "colleaguePersonId", value: "0001234"]
            ],
            roles: [[role: "instructor", startOn: "2020-08-15"]]
        ]

        // No instructor data in exchange properties

        // When
        def ctx = new TransformContext()
        def result = transforms.getMcInstructorTransformer().transform(ctx, JsonOutput.toJson(personData))

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)

        // Verify names structure
        assertEquals(1, result.names.size())
        assertEquals("John", result.names[0].firstName)
        assertEquals("Smith", result.names[0].lastName)

        assertEquals("instructor", result.status)

        // Verify default instructor data
        assertTrue(result.departments.isEmpty())
        assertTrue(result.instructorTypes.isEmpty())
    }

    @Test
    void testTransformInstructorWithPartialInstructorData() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "Jane Doe",
                firstName: "Jane",
                lastName: "Doe"
            ]],
            credentials: [
                [type: "colleaguePersonId", value: "0001235"]
            ],
            roles: [[role: "instructor", startOn: "2019-01-15"]]
        ]

        def instructorData = [
            id: "inst-8c005e2e-14fd-47f3-a772-eb88705818ef",
            instructor: [id: "8c005e2e-14fd-47f3-a772-eb88705818ef"],
            institutionalUnits: [
                [
                    department: [id: "dept-003", title: "English Literature"],
                    percentage: 100.0
                ]
            ]
            // No tenure or preferences data
        ]

        exchange.setProperty("ETHOS_ASSOC_INSTRUCTOR", [instructorData])

        // When
        def ctx = new TransformContext()
        ctx.contextProps.put("ETHOS_ASSOC_INSTRUCTOR", [instructorData])
        def result = transforms.getMcInstructorTransformer().transform(ctx, JsonOutput.toJson(personData))

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)

        // Verify names structure
        assertEquals(1, result.names.size())
        assertEquals("Jane", result.names[0].firstName)
        assertEquals("Doe", result.names[0].lastName)

        assertEquals("instructor", result.status)

        // Verify partial instructor data
        assertEquals(1, result.departments.size())
        assertEquals("dept-003", result.departments[0].id)
    }

    @Test
    void testTransformInstructorWithNonInstructorRole() {
        // Given
        def personData = [
            id: "8c005e2e-14fd-47f3-a772-eb88705818ef",
            names: [[
                type: [id: "legal"],
                fullName: "Student User",
                firstName: "Student",
                lastName: "User"
            ]],
            credentials: [
                [type: "colleaguePersonId", value: "0001236"]
            ],
            roles: [[role: "student", startOn: "2020-08-15"]]
        ]

        // When
        def ctx = new TransformContext()
        def result = transforms.getMcInstructorTransformer().transform(ctx, JsonOutput.toJson(personData))

        // Then
        assertNotNull(result)
        assertEquals("8c005e2e-14fd-47f3-a772-eb88705818ef", result.id)

        // Verify names structure
        assertEquals(1, result.names.size())
        assertEquals("Student", result.names[0].firstName)
        assertEquals("User", result.names[0].lastName)

        assertEquals("student", result.status) // Status from roles.role

        // Verify default instructor data
        assertTrue(result.departments.isEmpty())
        assertTrue(result.instructorTypes.isEmpty())
    }


}
