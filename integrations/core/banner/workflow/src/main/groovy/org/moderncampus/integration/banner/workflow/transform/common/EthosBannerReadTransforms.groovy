package org.moderncampus.integration.banner.workflow.transform.common

import groovy.json.JsonSlurper
import groovy.transform.CompileStatic
import org.moderncampus.integration.banner.workflow.route.Constants
import org.moderncampus.integration.dto.core.*
import org.moderncampus.integration.ellucian.component.internal.BannerEthosAPIResource
import org.moderncampus.integration.ellucian.workflow.transform.helper.EthosHelper
import org.moderncampus.integration.transform.ITransformer
import org.moderncampus.integration.transform.TransformContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

import java.time.ZonedDateTime

import static org.moderncampus.integration.transform.support.DateTimeFormatters.*

@Component
@CompileStatic
class EthosBannerReadTransforms {

    static Logger LOGGER = LoggerFactory.getLogger(EthosBannerReadTransforms.class)

    private static final String ACTIVE_STATUS = "A"

    ITransformer<String, MCOrganizationalUnit> ccOrgUnitTransformer = (ctx, String body) -> {
        MCOrganizationalUnit organizationalUnit = new MCOrganizationalUnit()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        organizationalUnit.id = rootNode.id
        organizationalUnit.code = rootNode.code
        organizationalUnit.name = rootNode.title
        organizationalUnit.description = rootNode.description
        organizationalUnit.type = rootNode.type
        organizationalUnit.parentId = ((rootNode?.parents as Map)?.unit as Map)?.id
        return organizationalUnit
    }

    ITransformer<String, MCSubject> mcSubjectTransformer = (ctx, String body) -> {
        MCSubject subject = new MCSubject()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        subject.id = rootNode.id
        subject.code = rootNode.abbreviation
        subject.title = rootNode.title
        return subject
    }

    ITransformer<String, MCInstructionalMethod> mcInstructionalMethodTransformer = (ctx, String body) -> {
        MCInstructionalMethod instructionMethod = new MCInstructionalMethod()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        instructionMethod.id = rootNode.id
        instructionMethod.title = rootNode.title
        return instructionMethod
    }

    ITransformer<String, MCLocation> mcLocationTransformer = (ctx, String body) -> {
        MCLocation location = new MCLocation()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        location.id = rootNode.id
        location.code = rootNode.code
        location.title = rootNode.title
        return location
    }

    ITransformer<String, MCAcademicLevel> ccAcademicLevelTransformer = (ctx, String body) -> {
        MCAcademicLevel academicLevel = new MCAcademicLevel()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        academicLevel.id = rootNode.id
        academicLevel.code = rootNode.code
        academicLevel.title = rootNode.title
        return academicLevel
    }

    ITransformer<String, MCRoom> mcRoomTransformer = (ctx, String body) -> {
        MCRoom room = new MCRoom()
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        room.id = rootNode.id
        room.code = rootNode.number
        room.title = rootNode.title
        room.roomTypes = rootNode.roomTypes?.collect { it ->
            return it['type'] as String
        }
        def capacityList = rootNode.occupancies?.collect { it ->
            return it['maxOccupancy'] as Integer
        }
        room.maxCapacity = capacityList.size() == 0 ? 0 : capacityList[0]
        room.location = rootNode?.site?['id']
        room.building = rootNode?.building?['id']
        room.characteristics = rootNode.roomCharacteristics?.collect { it ->
            MCRoomCharacteristic characteristic = new MCRoomCharacteristic()
            characteristic.id = it['id'] as String
            return characteristic
        }
        return room
    }

    ITransformer<String, MCSection> mcSectionTransformer = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        Map<BannerEthosAPIResource, Map<String, Map>> ethosAssocCacheMap = ctx.getContextProp(Constants.ETHOS_ASSOC_CACHE_MAP, Map.class)
        MCSection section = new MCSection()
        Map<String, Map> sectionTitleTypeMap = ethosAssocCacheMap[BannerEthosAPIResource.SECTION_TITLE_TYPES]
        Map<String, Map> sectionDescriptionTypeMap = ethosAssocCacheMap[BannerEthosAPIResource.SECTION_DESCRIPTION_TYPES]
        Map<String, Map> adminInstructionalMethodsTypeMap = ethosAssocCacheMap[BannerEthosAPIResource.ADMINISTRATIVE_INSTRUCTIONAL_METHODS]
        section.with {
            id = rootNode['id']
            rootNode['titles'].findAll {
                def sectionTitleTypeObj = sectionTitleTypeMap?[it['type']['id']]
                return sectionTitleTypeObj && sectionTitleTypeObj['code'] == 'short'
            }.each { titleObj ->
                shortTitle = titleObj['value']
            }
            rootNode['titles'].findAll {
                def sectionTitleTypeObj = sectionTitleTypeMap?[it['type']['id']]
                return sectionTitleTypeObj && sectionTitleTypeObj['code'] == 'long'
            }.each { titleObj ->
                longTitle = titleObj['value']
            }
            rootNode['descriptions']?.findAll {
                def sectionDescriptionObj = sectionDescriptionTypeMap?[it['type']['id']]
                return sectionDescriptionObj && sectionDescriptionObj['code'] == 'long'
            }?.each { descriptionObj ->
                description = descriptionObj['value']
            }
            rootNode['descriptions']?.findAll {
                def sectionDescriptionObj = sectionDescriptionTypeMap?[it['type']['id']]
                return sectionDescriptionObj && sectionDescriptionObj['code'] == 'short'
            }?.each { descriptionObj ->
                shortDescription = descriptionObj['value']
            }
            startOn = mapDate(rootNode['startOn'] as String)
            endOn = mapDate(rootNode['endOn'] as String)
            code = rootNode['code']
            number = rootNode['number']
            academicPeriod = rootNode['academicPeriod']?['id']
            censusDates = rootNode['censusDates']?.collect {
                return mapDate(it as String)
            }
            course = rootNode['course']['id']
            courseCategories = rootNode['courseCategories']?.collect {
                return it['id'] as String
            }
            rootNode['credits']?.findAll {
                return it?['creditCategory']['creditType'] == 'institution'
            }?.each { creditsObj ->
                minCredits = creditsObj['minimum'] as Integer
                maxCredits = creditsObj['maximum'] as Integer
            }
            rootNode['credits']?.findAll {
                return it['creditCategory']['creditType'] == 'ce'
            }?.each { creditsObj ->
                minCeu = creditsObj['minimum'] as Integer
                maxCeu = creditsObj['maximum'] as Integer
            }
            site = rootNode['site']?['id']
            academicLevels = rootNode['academicLevels']?.collect {
                return it['id'] as String
            }
            gradeSchemes = rootNode['gradeSchemes']?.collect {
                return it['id'] as String
            }
            instructionalMethods = rootNode['instructionalMethods']?.collect {
                return new MCSection.MCSectionInstructionalMethod(id: it['id'])
            }
            rootNode['hours']?.findAll {
                def instructionalMethodObj = adminInstructionalMethodsTypeMap?[it['administrativeInstructionalMethod']['id']]
                return instructionalMethodObj && instructionalMethodObj['code'] == 'contact'
            }?.each { hoursObj ->
                contactHours = hoursObj['minimum'] as Integer
            }
            rootNode['hours']?.findAll {
                def instructionalMethodObj = adminInstructionalMethodsTypeMap?[it['administrativeInstructionalMethod']['id']]
                return instructionalMethodObj && instructionalMethodObj['code'] == 'lab'
            }?.each { hoursObj ->
                labHours = hoursObj['minimum'] as Integer
            }
            rootNode['hours']?.findAll {
                def instructionalMethodObj = adminInstructionalMethodsTypeMap?[it['administrativeInstructionalMethod']['id']]
                return instructionalMethodObj && instructionalMethodObj['code'] == 'lecture'
            }?.each { hoursObj ->
                lectureHours = hoursObj['minimum'] as Integer
            }
            rootNode['hours']?.findAll {
                def instructionalMethodObj = adminInstructionalMethodsTypeMap?[it['administrativeInstructionalMethod']['id']]
                return instructionalMethodObj && instructionalMethodObj['code'] == 'other'
            }?.each { hoursObj ->
                otherHours = hoursObj['minimum'] as Integer
            }
            instructionalDeliveryMethod = rootNode['instructionalDeliveryMethod']?['id']
            status = rootNode['status']?['detail']?['id']
            duration = rootNode['duration']?['length'] as Integer
            durationUnits = rootNode['duration']?['unit']
            maxEnrollment = rootNode['maxEnrollment'] as Integer
            crossListed = rootNode['crossListed']
            owningInstitutionUnits = rootNode['owningInstitutionUnits']?.collect {
                return new MCInstitutionUnit(id: it['institutionUnit']['id'])
            }
            billingHours = rootNode['billing'] as Integer
            otherIds = rootNode['alternateIds']?.collect {
                return new MCAlternateId(type: it['title'], value: it['value'])
            }
            reportingAcademicPeriod = rootNode['reportingAcademicPeriod']?['id']
        }
        return section
    }

    ITransformer<String, MCSectionSchedule> mcSectionScheduleTransformer = (ctx, String body) -> {

        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        Optional<ZonedDateTime> startOn = Optional.ofNullable(rootNode?.recurrence?['timePeriod']?['startOn'] as String)
                .map(dt -> fromUtcDateTimeString(dt))
        Optional<ZonedDateTime> endOn = Optional.ofNullable(rootNode?.recurrence?['timePeriod']?['endOn'] as String)
                .map(dt -> fromUtcDateTimeString(dt))

        List<String> roomIdArray = rootNode?.locations?['location']?['room']?['id'] as List<String>

        def sectionSchedule = new MCSectionSchedule(
                id: rootNode.id,
                room: roomIdArray?.get(0),
                section: rootNode.section?['id'],
                instructionalMethod: rootNode.instructionalMethod?['id'],
                recurrenceType: rootNode?.recurrence?['repeatRule']?['type'],
                recurrenceInterval: rootNode.recurrence?['repeatRule']?['interval'] as Integer,
                startTime: startOn.map { it.toOffsetDateTime().toOffsetTime() }.orElse(null),
                endTime: endOn.map { it.toOffsetDateTime().toOffsetTime() }.orElse(null),
                startDate: startOn.map { it.toLocalDate() }.orElse(null),
                endDate: endOn.map { it.toLocalDate() }.orElse(null)
        )

        List<String> daysOfWeek = rootNode.recurrence?['repeatRule']['daysOfWeek'] as List<String>
        def daysToFill = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]

        daysToFill.each {
            def dayVariable = "day${it.capitalize()}"
            sectionSchedule[dayVariable] = daysOfWeek.contains(it)
        }

        return sectionSchedule
    }

    ITransformer<String, MCAcademicPeriod> mcAcademicPeriodTransformer = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map
        List<Map<String, ?>> ethosYears = ctx.getContextProp(Constants.ETHOS_ASSOC_YEAR, List<Map>.class)
        Map<String, Map> expandedYearsMap = EthosHelper.groupEthosResultById(ethosYears)
        List<String> censusDates = rootNode.censusDates as List<String>
        if ("year" == rootNode['category']['type']) {
            return null
        }
        def period = new MCAcademicPeriod(
                id: rootNode?.id,
                code: rootNode?.code,
                name: rootNode?.title,
                startDate: mapDateTimeWithOffset(rootNode?.startOn as String),
                endDate: mapDateTimeWithOffset(rootNode?.endOn as String),
                partsOfTerm: rootNode.category?['parent']?['id'],
                censusDates: censusDates?.collect { mapDate(it) }
        )
        if (rootNode['category']['parent']?['academicPeriod']?['id']) {
            Map yearAssoc = expandedYearsMap[rootNode['category']['parent']?['academicPeriod']?['id']]
            if (yearAssoc) {
                period.year = yearAssoc['code']
            }
        }
        return period
    }

    ITransformer<String, MCInstructor> mcInstructorTransformer = (TransformContext ctx, String body) -> {
        def ethosPerson = new JsonSlurper().parseText(body) as Map
        List<Map> ethosInstructorAssocs = ctx.getContextProp(Constants.ETHOS_ASSOC_INSTRUCTOR, List<Map>.class)
        if (!ethosInstructorAssocs) {
            LOGGER.info("Unable to continue mapping instructor, as ethos person does not have a valid instructor record for id: " + ethosPerson['id'])
        }
        MCInstructor mcInstructor = new MCInstructor()
        mcInstructor.with {
            id = ethosPerson['id']
            names = ethosPerson['names'].collect { ethosName ->
                new MCInstructor.MCInstructorName().with {
                    preferred = ethosName['preference'] as Boolean
                    if (ethosName['type']?['detail']?['id'] || ethosName['type']?['category']) {
                        type = new MCInstructor.MCInstructorNameType().with {
                            id = ethosName['type']?['detail']?['id']
                            name = ethosName['type']?['category']
                            it
                        }
                    }
                    title = ethosName['title']
                    firstName = ethosName['firstName']
                    lastName = ethosName['lastName']
                    middleName = ethosName['middleName']
                    suffix = ethosName['lastName'] // Map from names.lastName (ethos)
                    it
                }
            }
            emails = ethosPerson['emails']?.collect { ethosEmail ->
                new MCInstructor.MCInstructorEmail().with {
                    preferred = ethosEmail['preference'] as Boolean
                    type = new MCInstructor.MCInstructorEmailType().with {
                        id = ethosEmail['type']['detail']?['id']
                        name = ethosEmail['type']['emailType']
                        it
                    }
                    address = ethosEmail['address']
                    it
                }
            }

            List<MCInstructor.MCInstructorType> instructorTypeList
            List<MCInstructor.MCInstructorDepartment> instructorDepartmentList
            ethosInstructorAssocs.each { instructorAssoc ->
                if (instructorAssoc['category']) {
                    if (!instructorTypeList) {
                        instructorTypeList = []
                    }
                    instructorTypeList.add(new MCInstructor.MCInstructorType().with {
                        id = instructorAssoc['category']['id']
                        it
                    })

                    // Both instructorTypes and departments map from the same field: instructors.category.id (ethos)
                    if (!instructorDepartmentList) {
                        instructorDepartmentList = []
                    }
                    instructorDepartmentList.add(new MCInstructor.MCInstructorDepartment().with {
                        id = instructorAssoc['category']['id']
                        it
                    })
                }
            }
            instructorTypes = instructorTypeList
            departments = instructorDepartmentList
            def instructorRole = ethosPerson['roles']?.find {
                "instructor" == it['role']
            }
            status = instructorRole ? instructorRole['role'] : "A"
            it
        }

    }

    ITransformer<String, MCSectionInstructorAssignment> mcSectionInstructorAssignmentTransformer = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def assignment = new MCSectionInstructorAssignment()

        assignment.id = rootNode.id
        assignment.workloadHrs = rootNode.workLoad as Integer
        assignment.assignmentStartOn = mapDate(rootNode.workStartOn as String)
        assignment.assignmentEndOn = mapDate(rootNode.workEndOn as String)
        assignment.percentageResponsible = rootNode.responsibilityPercentage as Integer
        assignment.instructorRole = rootNode.instructorRole

        assignment.instructorId = rootNode.instructor?['id']
        assignment.sectionId = rootNode.section?['id']
        assignment.instructionalMethod = rootNode.instructionalMethod?['id']

        return assignment
    }

    ITransformer<String, MCCourseSectionInformation> mcCourseSectionInformation = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        def courseSectionInformation = new MCCourseSectionInformation()

        courseSectionInformation.id = rootNode.id
        courseSectionInformation.xlstGroup = rootNode.xlstGroup

        return courseSectionInformation
    }

    ITransformer<String, MCSectionCrossListGroup> mCSectionCrossListGroup = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        List<Map<String, Object>> groupsMap = rootNode['SSBXLST'] as List<Map<String, Object>>
        if (groupsMap.size() > 1) {
            throw new IllegalArgumentException("Expecting a single result in response, but got a list of results.");
        }

        Map<String, Object> result = groupsMap.first()
        return mapSectionCrossListGroup(result)
    }

    ITransformer<String, List<MCSectionCrossListGroup>> mCSectionCrossListGroups = (TransformContext ctx, String body) -> {
        def rootNode = new JsonSlurper().parseText(body) as Map<String, ?>
        List<Map<String, Object>> groupsMap = rootNode['SSBXLST'] as List<Map<String, Object>>

        def results = groupsMap?.collect {
            mapSectionCrossListGroup(it)
        }

        return results
    }

    static MCSectionCrossListGroup mapSectionCrossListGroup(Map<String, Object> it) {
        def group = new MCSectionCrossListGroup()
        group.termCode = it.termCode
        group.groupCode = it.xlstGroup
        group.sectionCode = it.crn
        group.maxEnrollments = it.maxEnrl as BigDecimal
        group.id = group.groupCode + "|" + group.termCode
        return group
    }
}
